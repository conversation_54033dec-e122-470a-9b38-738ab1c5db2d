<template>
  <div ref="wrapRef" :style="{ height, width }"></div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue';
import { useScript } from '/@/hooks/web/useScript';

const A_MAP_URL = 'https://webapi.amap.com/maps?v=2.0&key=06313eb9c6563b674a8fd789db0692c3&plugin=AMap.MarkerClusterer';

// 定义 props
const props = defineProps({
  width: {
    type: String,
    default: '100%',
  },
  height: {
    type: String,
    default: 'calc(100vh - 78px)',
  },
  mapOptions: {
    type: Object,
    default: () => ({
      zoom: 14.5,
      center: [121.34113, 31.1959],
      viewMode: '3D',
      layers: [],
    }),
  },
});

// 暴露方法
const mapInstance = ref<any>(null);
defineExpose({
  map: mapInstance,
});

const wrapRef = ref<HTMLDivElement | null>(null);
const { toPromise } = useScript({ src: A_MAP_URL });

async function initMap() {
  await toPromise();
  await nextTick();

  const wrapEl = wrapRef.value;
  if (!wrapEl) return;

  const AMap = (window as any).AMap;
  const map = new AMap.Map(wrapEl, {
    ...props.mapOptions,
  });
    // 加载 moveAnimation 插件
  AMap.plugin('AMap.MoveAnimation', () => {
    console.log('AMap.MoveAnimation 插件加载成功');
  });
  // 初始化时直接加载卫星图层
      const satelliteLayer = new AMap.TileLayer.Satellite();
      satelliteLayer.setMap(map);
  // 示例：限制地图范围
  const bounds = map.getBounds();
  map.setLimitBounds(bounds);

  // // 示例：添加多边形
  // const path1 = [
  //   [121.3461388860842, 31.18920317889403],
  //   [121.34642000201387, 31.189203179797687],
  //   [121.34642350635379, 31.18899171107705],
  //   [121.34613810792094, 31.188991494219145],
  // ];

  // const polygon1 = new AMap.Polygon({
  //   path: path1,
  //   strokeColor: '#1791fc',
  //   strokeWeight: 6,
  //   strokeOpacity: 0.2,
  //   fillOpacity: 0.4,
  //   fillColor: '#1791fc',
  //   zIndex: 50,
  //   bubble: true,
  // });

  // map.add([polygon1]);

  mapInstance.value = map; // 保存地图实例
}

onMounted(() => {
  initMap();
});
</script>