import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '阅读器地址',
    align:"center",
    dataIndex: 'ipAddress'
   },
   {
    title: '阅读器名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '阅读器IMEI编号',
    align:"center",
    dataIndex: 'imeiNumber'
   },
   {
    title: '经度',
    align:"center",
    dataIndex: 'longitude'
   },
   {
    title: '纬度',
    align:"center",
    dataIndex: 'latitude'
   },
   {
    title: '状态',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '是否门口',
    align:"center",
    dataIndex: 'doorType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
   {
    title: '入口天线',
    align:"center",
    dataIndex: 'inletAntenna'
   },
   {
    title: '出口天线',
    align:"center",
    dataIndex: 'qutletAntenna'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '阅读器地址',
    field: 'ipAddress',
    component: 'Input',
  },
  {
    label: '阅读器名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '阅读器IMEI编号',
    field: 'imeiNumber',
    component: 'Input',
  },
  {
    label: '经度',
    field: 'longitude',
    component: 'Input',
  },
  {
    label: '纬度',
    field: 'latitude',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '是否门口',
    field: 'doorType',
     component: 'JSwitch',
     componentProps:{
     },
  },
  {
    label: '入口天线',
    field: 'inletAntenna',
    component: 'Input',
  },
  {
    label: '出口天线',
    field: 'qutletAntenna',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  ipAddress: {title: '阅读器地址',order: 0,view: 'text', type: 'string',},
  name: {title: '阅读器名称',order: 1,view: 'text', type: 'string',},
  imeiNumber: {title: '阅读器IMEI编号',order: 2,view: 'text', type: 'string',},
  longitude: {title: '经度',order: 3,view: 'text', type: 'string',},
  latitude: {title: '纬度',order: 4,view: 'text', type: 'string',},
  status: {title: '状态',order: 5,view: 'text', type: 'string',},
  remark: {title: '备注',order: 6,view: 'text', type: 'string',},
  doorType: {title: '是否门口',order: 7,view: 'switch', type: 'string',},
  inletAntenna: {title: '入口天线',order: 8,view: 'text', type: 'string',},
  qutletAntenna: {title: '出口天线',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}