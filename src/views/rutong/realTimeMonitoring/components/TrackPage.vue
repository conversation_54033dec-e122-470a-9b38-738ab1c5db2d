<template>
  <div class="track-page">
    <!-- 时间范围选择 -->
    <a-form-item label="轨迹时间:" :labelCol="{ span: 10 }" :wrapperCol="{ span: 14 }">
      <a-range-picker
        value-format="YYYY-MM-DD HH:mm:ss"
        show-time
        :disabledDate="disabledDateHandler"
        @calendarChange="handleCalendarChange"
        v-model:value="dateRange"
      />
    </a-form-item>
    <div class="search-button-wrapper">
      <button class="btn search-btn" @click="searchData">搜索</button>
    </div>

    <!-- 地图展示 -->
    <Gaode ref="mapRef" :width="'100%'" :height="'800px'" :mapOptions="mapOptions" />

    <!-- 控制按钮 -->
    <div class="control-panel">
      <div class="control-group">
        <button class="btn" @click="startAnimation">开始动画</button>
        <button class="btn" @click="pauseAnimation">暂停动画</button>
      </div>
      <div class="control-group">
        <button class="btn" @click="resumeAnimation">继续动画</button>
        <button class="btn" @click="stopAnimation">停止动画</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Gaode from '/@/views/demo/charts/map/Gaode.vue';
import { rfidHistoricalRoute } from '../BaseEquipmentManagement.api';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { throttle } from 'lodash-es'; // 使用 lodash 的节流函数

const route = useRoute();
const router = useRouter();
const epc = route.query.epc; // 从查询参数获取设备编号

const mapRef = ref<any>(null);
const mapOptions = {
  zoom: 14.5,
  center: [121.34113, 31.1959],
  viewMode: '3D',
};

const dateRange = ref<string[]>([]); // 日期范围
let marker: any = null; // 地图标记
let lineArr: [number, number][] = []; // 轨迹点数组
let passedPolyline: any = null; // 已走过的轨迹线
let fullPolyline: any = null; // 完整的轨迹线

const animationState = ref('stopped'); // 动画状态：'playing', 'paused', 'stopped'

/**
 * 搜索按钮点击后，加载数据
 */
function searchData() {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择完整的时间范围');
    return;
  }
  initializeMapData(); // 加载轨迹数据
}

/**
 * 初始化地图和轨迹数据
 */
async function initializeMapData() {
  const mapInstance = mapRef.value?.map;
  if (!mapInstance) {
    console.error('地图实例未加载');
    return;
  }

  // 清空之前的轨迹和标记
  mapInstance.clearMap();
  marker = null;
  lineArr = [];

  const queryParams = {
    epc,
    startTime: dateRange.value?.[0] || '',
    endTime: dateRange.value?.[1] || '',
  };

  try {
    const response = await rfidHistoricalRoute(queryParams);
    if (!response) {
      ElMessage.error('未获取到轨迹数据');
      return;
    }

    // 绘制区域和点位
    drawAreasAndPoints(mapInstance, response.areaData);

    // 绘制轨迹线
    drawTrajectory(mapInstance, response.dataList);
  } catch (error) {
    console.error('获取轨迹数据失败:', error);
    ElMessage.error('加载轨迹数据失败，请稍后重试');
  }
}

/**
 * 绘制区域和点位
 */
function drawAreasAndPoints(mapInstance: any, areaData: any[]) {
  const AMap = (window as any).AMap;
  const points: any[] = [];

  areaData.forEach((item) => {
    const path = [
      item.coordinate.split(',').map(Number),
      item.coordinate2.split(',').map(Number),
      item.coordinate3.split(',').map(Number),
      item.coordinate4.split(',').map(Number),
    ];

    // 绘制多边形区域
    const polygon = new AMap.Polygon({
      path,
      strokeColor: item.color,
      strokeWeight: 6,
      strokeOpacity: 0.2,
      fillOpacity: 0.4,
      fillColor: item.color,
      zIndex: 50,
      bubble: true,
    });
    mapInstance.add(polygon);

    // 添加区域文本
    const center = polygon.getBounds().getCenter();
    const text = new AMap.Text({
      text: item.name,
      anchor: 'center',
      style: {
        color: item.color,
        fontSize: '14px',
        fontWeight: 'bold',
      },
      position: center,
    });
    mapInstance.add(text);

    points.push({ lnglat: [center.lng, center.lat] });
  });

  // 添加点位集群
  new AMap.MarkerCluster(mapInstance, points, { gridSize: 60 });
}

/**
 * 绘制轨迹线
 */
function drawTrajectory(mapInstance: any, dataList: any[]) {
  const AMap = (window as any).AMap;

  dataList.forEach((item) => {
    item.data.forEach((point) => {
      const latitude = Number(point.latitude);
      const longitude = Number(point.longitude);
      lineArr.push([longitude, latitude]);
    });
  });

  // 创建轨迹标记
  marker = new AMap.Marker({
    map: mapInstance,
    position: lineArr[0],
    icon: 'https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png',
  });

  // 完整轨迹线
  fullPolyline = new AMap.Polyline({
    map: mapInstance,
    path: lineArr,
    strokeColor: '#28F',
    strokeWeight: 6,
  });

  // 走过的轨迹线
  passedPolyline = new AMap.Polyline({
    map: mapInstance,
    strokeColor: '#AF5',
    strokeWeight: 6,
  });

  // 设置地图视野
  mapInstance.setFitView();
}

/**
 * 动画控制逻辑
 */
function startAnimation() {
  if (marker && lineArr.length > 0) {
    marker.moveAlong(lineArr, {
      duration: 20000,
      autoRotation: true,
    });
  }
}

function pauseAnimation() {
  if (marker) {
    marker.pauseMove();
    animationState.value = 'paused';
  }
}

function resumeAnimation() {
  if (marker) {
    marker.resumeMove();
    animationState.value = 'playing';
  }
}

function stopAnimation() {
  if (marker) {
    marker.stopMove();
    animationState.value = 'stopped';
    passedPolyline.setPath([]); // 清空已走过的路径
  }
}

/**
 * 日期范围选择逻辑
 */
function handleCalendarChange(dates: any[]) {
  if (dates && dates[0]) {
    dateRange.value = [dates[0], null];
  }
}

/**
 * 日期选择禁用逻辑
 */
function disabledDateHandler(current: any) {
  if (!current) return false;
  if (!dateRange.value.length) return false;

  const startDate = dayjs(dateRange.value[0]);
  return current.isBefore(startDate.startOf('day')) || current.isAfter(startDate.add(7, 'day').endOf('day'));
}

onMounted(() => {
  const checkMapLoaded = setInterval(() => {
    if (mapRef.value?.map) {
      clearInterval(checkMapLoaded);
      initializeMapData();
    }
  }, 100);
});
</script>

<style scoped>
.track-page {
  padding: 16px;
}

.search-button-wrapper {
  text-align: center;
  margin-bottom: 16px;
}

.control-panel {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.control-group {
  display: flex;
  margin-bottom: 1rem;
}

.btn {
  margin-right: 1rem;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: #0056b3;
}

.btn:active {
  background-color: #003f7f;
}

.search-btn {
  width: 120px;
}
</style>