<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol" name="BaseEquipmentManagementForm">
          <a-row>
						<a-col :span="24">
							<a-form-item label="设备编号" v-bind="validateInfos.number" id="BaseEquipmentManagementForm-number" name="number">
								<a-input v-model:value="formData.number" placeholder="请输入设备编号"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="标签EPC" v-bind="validateInfos.epcNumber" id="BaseEquipmentManagementForm-epcNumber" name="epcNumber">
								<j-dict-select-tag v-model:value="formData.epcNumber" dictCode="base_tag ,epc_number,epc_number" placeholder="请选择标签EPC"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="设备类型" v-bind="validateInfos.type" id="BaseEquipmentManagementForm-type" name="type">
								<j-dict-select-tag v-model:value="formData.type" dictCode="base_equipment_type,name,name" placeholder="请选择设备类型"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="所属公司" v-bind="validateInfos.affiliatedCompany" id="BaseEquipmentManagementForm-affiliatedCompany" name="affiliatedCompany">
								<j-dict-select-tag v-model:value="formData.affiliatedCompany" dictCode="sys_depart where org_category = 1,depart_name,org_code" placeholder="请选择所属公司"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="当前位置" v-bind="validateInfos.currentPosition" id="BaseEquipmentManagementForm-currentPosition" name="currentPosition">
								<a-input v-model:value="formData.currentPosition" placeholder="请输入当前位置"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="设备状态" v-bind="validateInfos.status" id="BaseEquipmentManagementForm-status" name="status">
								<a-input v-model:value="formData.status" placeholder="请输入设备状态"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="设备图片" v-bind="validateInfos.imageInfo" id="BaseEquipmentManagementForm-imageInfo" name="imageInfo">
								<j-image-upload :fileMax="0" v-model:value="formData.imageInfo" ></j-image-upload>
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../BaseEquipmentManagement.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import { duplicateValidate } from '/@/utils/helper/validator'
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    number: '',   
    epcNumber: '',   
    type: '',   
    affiliatedCompany: '',   
    currentPosition: '',   
    status: '',   
    imageInfo: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    epcNumber: [{ required: true, message: '请输入标签EPC!'}, { validator: epcNumberDuplicatevalidate }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  async function epcNumberDuplicatevalidate(_r, value) {
    return duplicateValidate('base_equipment_management', 'epc_number', value, formData.id || '')
  }
  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
