import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  rfidHistoricalRoute = '/rutong/baseEquipmentManagementInfo/rfidHistoricalRoute',
  listAll = '/rutong/baseEquipmentManagementInfo/listAll',
  list = '/rutong/baseEquipmentManagementInfo/list',
  getEquipment = '/rutong/baseEquipmentManagementInfo/getEquipment',
  save='/rutong/baseEquipmentManagementInfo/add',
  edit='/rutong/baseEquipmentManagementInfo/edit',
  deleteOne = '/rutong/baseEquipmentManagementInfo/delete',
  deleteBatch = '/rutong/baseEquipmentManagementInfo/deleteBatch',
  importExcel = '/rutong/baseEquipmentManagementInfo/importExcel',
  exportXls = '/rutong/baseEquipmentManagementInfo/exportXls',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });
/**
 * 所有设备定位信息
 * @param params
 */
export const listAll = (params) => defHttp.get({ url: Api.listAll, params });

/**
 * 所有设备定位信息
 * @param params
 */
export const rfidHistoricalRoute = (params) => defHttp.get({ url: Api.rfidHistoricalRoute, params });
/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}
