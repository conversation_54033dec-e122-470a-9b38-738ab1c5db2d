import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [

  {
    title: '设备编号',
    align: "center",
    dataIndex: 'number',
    width:120
  },
 
  {
    title: '类型',
    align: "center",
    dataIndex: 'type_dictText',
    width:100
  },
  
  {
    title: '位置',
    align: "center",
    dataIndex: 'currentPosition',
    width:120
  },
];

// 高级查询数据
export const superQuerySchema = {
  updateTime: {title: '更新日期',order: 0,view: 'datetime', type: 'string',},
  number: {title: '设备编号',order: 1,view: 'text', type: 'string',},
  epcNumber: {title: '标签EPC',order: 2,view: 'list', type: 'string',dictTable: "base_tag ", dictCode: 'epc_number', dictText: 'epc_number',},
  type: {title: '设备类型',order: 3,view: 'list', type: 'string',dictTable: "base_equipment_type", dictCode: 'name', dictText: 'name',},
  affiliatedCompany: {title: '所属公司',order: 4,view: 'list', type: 'string',dictTable: "sys_depart where org_category = 1", dictCode: 'org_code', dictText: 'depart_name',},
  currentPosition: {title: '当前位置',order: 5,view: 'text', type: 'string',},
  status: {title: '设备状态',order: 6,view: 'text', type: 'string',},
  imageInfo: {title: '设备图片',order: 7,view: 'image', type: 'string',},
};
