<template>
  <div class="p-2">
    <a-row :gutter="24" style="height: 85vh">
      <a-col :span="8">
        <!--查询区域-->
        <div class="jeecg-basic-table-form-container">
          <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row class="countdown-row">
              <a-col>
                <span>
                  <span class="countdown-number">{{ countdown }}</span>
                  秒后刷新
                </span>
              </a-col>
            </a-row>
            <a-row class="search-row">
              <a-col :span="18">
                <a-input v-model:value="queryParam.name" placeholder="设备编号" class="search-input" />
              </a-col>
              <a-col :span="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery" class="search-button"> </a-button>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <!--引用表格-->
        <BasicTable @register="registerTable">
          <!--插槽:table标题-->
          <template #tableTitle>
            <!-- <a-button type="primary" v-auth="'rutong:base_equipment_management:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">
              新增</a-button
            > -->
            <!-- <a-button type="primary" v-auth="'rutong:base_equipment_management:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls">
              导出</a-button
            > -->
            <!-- <j-upload-button
              type="primary"
              v-auth="'rutong:base_equipment_management:importExcel'"
              preIcon="ant-design:import-outlined"
              @click="onImportXls"
              >导入</j-upload-button
            > -->
            <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button v-auth="'rutong:base_equipment_management:deleteBatch'"
                >批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
            </a-dropdown>
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <a @click="openTrackPage(record)">轨迹</a>
          </template>
          <template v-slot:bodyCell="{ column, record, index, text }"> </template>
        </BasicTable>
        <!-- 表单区域 -->
        <BaseEquipmentManagementModal ref="registerModal" @success="handleSuccess"></BaseEquipmentManagementModal>
      </a-col>
      <!-- 右侧地图区域 -->
      <a-col :span="16">
        <Gaode ref="mapRef2" :width="'100%'" :height="'100%'" :mapOptions="mapOptions" />
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" name="rutong-baseEquipmentManagement" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';

import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, superQuerySchema } from './BaseEquipmentManagement.data';
import { listAll, list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './BaseEquipmentManagement.api';
import { list as listArea } from '../BaseArea.api';

import { downloadFile } from '/@/utils/common/renderUtils';
import BaseEquipmentManagementModal from './components/BaseEquipmentManagementModal.vue';
import { useUserStore } from '/@/store/modules/user';
import Gaode from '/@/views/demo/charts/map/Gaode.vue';
import { useRouter } from 'vue-router';

const formRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const registerModal = ref();
const userStore = useUserStore();
const trackModalVisible = ref(false); // 控制弹框的显示状态
const selectedRecord = ref(null); // 当前选中的记录

const router = useRouter();

// 打开轨迹页面
function openTrackPage(record: any) {
  // 使用 query 传参
  router.push({
    path: '/rutong/realTimeMonitoring/TrackPage',
    query: {
      epc: record.epcNumber, // 传递 id 参数
    },
  });
}

//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '设备管理',
    api: list,
    columns,
    canResize: false,
    useSearchForm: false,
    actionColumn: {
      width: 80,
    },
    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  },
  exportConfig: {
    name: '设备管理',
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess,
  },
});
const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
  tableContext;
const labelCol = reactive({
  xs: 24,
  sm: 4,
  xl: 6,
  xxl: 4,
});
const wrapperCol = reactive({
  xs: 24,
  sm: 20,
});
const countdown = ref<number>(30);
const timer = ref<ReturnType<typeof setInterval> | null>(null); // 使用 ref 管理定时器

function startCountdown() {
  countdown.value = 30;

  // 清理可能遗留的旧定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }

  // 启动新定时器
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value -= 1;
    } else {
      clearInterval(timer.value!);
      timer.value = null;
      reload(); // 调用刷新方法
      startCountdown(); // 重启倒计时
    }
  }, 1000);
}

// 页面加载后启动倒计时
onMounted(() => {
  startCountdown();
  initializeTableData();
});

// 清理计时器
onUnmounted(() => {
  if (timer) clearInterval(timer);
});

//地图------------------------

const mapRef2 = ref<any>(null);

const mapOptions = {
  zoom: 14.5,
  center: [121.34113, 31.1959],
  viewMode: '3D',
};

function logMapCenter() {
  const map = mapRef2.value?.map; // 获取子组件暴露的 map 实例
  if (map) {
    console.log('当前地图中心:', map.getCenter());
  }
}
// 显式声明 points 数组的类型
interface Point {
  lnglat: string[]; // 每个元素的 lnglat 属性是一个字符串数组
}
async function initializeTableData() {
  await reload(); // 等待数据加载完成
  const tableData = await listArea(null); // 获取数据
  const equipmentList = await listAll(queryParam); // 获取数据

  const map2 = mapRef2.value?.map; // 获取子组件暴露的 map 实例
  console.log(tableData, '11111111');

  const AMap = (window as any).AMap;
  if (!AMap) {
    console.error('未能找到 AMap 对象，请检查高德地图脚本是否正确加载');
    return;
  }
  var gridSize = 60;

  let points: Point[] = [];

  equipmentList.forEach((item, index) => {
    console.log(item.longitude, item.latitude);
    if(item.longitude!=null && item.latitude != null){
      points.push({ lnglat: [item.longitude, item.latitude] });
    }
  });
  console.log(points, '3344444444444444');

  tableData.records.forEach((item, index) => {
    console.log(`第 ${index + 1} 条数据:`, item);
    // 你可以根据需要在这里对 item 做更多的处理
    // 示例：添加多边形
    // 解析字符串坐标
    const path1 = [
      item.coordinate.split(',').map(Number),
      item.coordinate2.split(',').map(Number),
      item.coordinate3.split(',').map(Number),
      item.coordinate4.split(',').map(Number),
    ];
    const polygon1 = new AMap.Polygon({
      path: path1,
      strokeColor: item.color,
      strokeWeight: 6,
      strokeOpacity: 0.2,
      fillOpacity: 0.4,
      fillColor: item.color,
      zIndex: 50,
      bubble: true,
    });
    map2.add([polygon1]);

    // 添加文字标注
    const center = polygon1.getBounds().getCenter(); // 获取多边形中心点
    const text = new AMap.Text({
      text: item.name, // 标注内容
      anchor: 'center', // 设置文本居中
      draggable: false, // 禁止拖拽
      style: {
        color: item.color, // 文字颜色
        fontSize: '14px', // 文字大小
        fontWeight: 'bold', // 文字加粗
        // backgroundColor: 'transparent', // 背景透明
        border: 'none', // 无边框
      },
      position: center, // 设置文字位置
    });

    map2.add(text); // 将文字添加到地图
  });
  new AMap.MarkerCluster(map2, points, { gridSize: gridSize });
  console.log('初始化表格数据:', tableData);
}

// 高级查询配置
const superQueryConfig = reactive(superQuerySchema);

/**
 * 高级查询事件
 */
function handleSuperQuery(params) {
  Object.keys(params).map((k) => {
    queryParam[k] = params[k];
  });
  searchQuery();
}

/**
 * 新增事件
 */
function handleAdd() {
  registerModal.value.disableSubmit = false;
  registerModal.value.add();
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  registerModal.value.disableSubmit = false;
  registerModal.value.edit(record);
}

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  registerModal.value.disableSubmit = true;
  registerModal.value.edit(record);
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      auth: 'rutong:base_equipment_management:edit',
    },
  ];
}

/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
        placement: 'topLeft',
      },
      auth: 'rutong:base_equipment_management:delete',
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  reload();
  if (timer) clearInterval(timer);
  startCountdown();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}
</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;
  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
  .query-group-cust {
    min-width: 100px !important;
  }
  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center;
  }
  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
}
.countdown-row {
  margin-bottom: 10px;
  .countdown-number {
    font-size: 20px;
    font-weight: bold;
    color: #007aff; /* 蓝色 */
  }
  span {
    font-size: 14px;
    color: #333; /* 黑色 */
  }
}
</style>
