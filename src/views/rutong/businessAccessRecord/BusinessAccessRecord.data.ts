import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '设备编号',
    align:"center",
    dataIndex: 'number'
   },
   {
    title: 'RFID标签号',
    align:"center",
    dataIndex: 'epcNumber'
   },
   {
    title: '位置信息',
    align:"center",
    dataIndex: 'location'
   },
   {
    title: '设备类型',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '所属公司',
    align:"center",
    dataIndex: 'affiliatedCompany'
   },
   {
    title: '所在部门',
    align:"center",
    dataIndex: 'affiliatedDept'
   },
   {
    title: '出入时间',
    align:"center",
    sorter: true,
    dataIndex: 'createTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '设备编号',
    field: 'number',
    component: 'Input',
  },
  {
    label: 'RFID标签号',
    field: 'epcNumber',
    component: 'Input',
  },
  {
    label: '位置信息',
    field: 'location',
    component: 'Input',
  },
  {
    label: '设备类型',
    field: 'type',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  number: {title: '设备编号',order: 0,view: 'text', type: 'string',},
  epcNumber: {title: 'RFID标签号',order: 1,view: 'text', type: 'string',},
  location: {title: '位置信息',order: 2,view: 'text', type: 'string',},
  type: {title: '设备类型',order: 3,view: 'text', type: 'string',},
  createTime: {title: '出入时间',order: 4,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}