import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { JInput } from '/@/components/Form';
//列表数据
export const columns: BasicColumn[] = [
  // {
  //   title: '读写器信息',
  //   align: "center",
  //   dataIndex: 'readerInfo_dictText'
  // },
  {
    title: '名称',
    align: "center",
    dataIndex: 'name'
  },
  // {
  //   title: '颜色',
  //   align: "center",
  //   dataIndex: 'color'
  // },
  // {
  //   title: '经纬度',
  //   align: "center",
  //   dataIndex: 'coordinate'
  // },
  // {
  //   title: '经纬度2',
  //   align: "center",
  //   dataIndex: 'coordinate2'
  // },
  // {
  //   title: '经纬度3',
  //   align: "center",
  //   dataIndex: 'coordinate3'
  // },
  // {
  //   title: '经纬度4',
  //   align: "center",
  //   dataIndex: 'coordinate4'
  // },
];

// 高级查询数据
export const superQuerySchema = {
  readerInfo: {title: '读写器信息',order: 0,view: 'list', type: 'string',dictTable: "base_reader", dictCode: 'name', dictText: 'name',},
  name: {title: '名称',order: 1,view: 'text', type: 'string',component:'JInput'},
  color: {title: '颜色',order: 2,view: 'text', type: 'string',},
  coordinate: {title: '经纬度',order: 3,view: 'text', type: 'string',},
  coordinate2: {title: '经纬度2',order: 4,view: 'text', type: 'string',},
  coordinate3: {title: '经纬度3',order: 5,view: 'text', type: 'string',},
  coordinate4: {title: '经纬度4',order: 6,view: 'text', type: 'string',},
};
