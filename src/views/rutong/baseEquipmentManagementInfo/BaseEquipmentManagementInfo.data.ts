import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '设备编号',
    align:"center",
    dataIndex: 'number'
   },
   {
    title: '标签EPC',
    align:"center",
    dataIndex: 'epcNumber'
   },
   {
    title: '设备类型',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '所属公司',
    align:"center",
    dataIndex: 'affiliatedCompany'
   },
   {
    title: '所在部门',
    align:"center",
    dataIndex: 'affiliatedDept'
   },
   {
    title: '当前位置',
    align:"center",
    dataIndex: 'currentPosition'
   },
   {
    title: '设备状态',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '更新日期',
    align:"center",
    sorter: true,
    dataIndex: 'updateTime'
   },
   {
    title: '设备图片',
    align:"center",
    dataIndex: 'imageInfo',
    customRender:render.renderImage,
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '设备编号',
    field: 'number',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入设备编号!'},
          ];
     },
  },
  {
    label: '标签EPC',
    field: 'epcNumber',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"base_tag where status = 0,epc_number,epc_number"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入标签EPC!'},
                 {...rules.duplicateCheckRule('base_equipment_management', 'epc_number',model,schema)[0]},
          ];
     },
  },
  {
    label: '设备类型',
    field: 'type',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"base_equipment_type,name,name"
     },
  },
  {
    label: '所属公司',
    field: 'affiliatedCompany',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"sys_depart where org_category = 1 and org_code =  '${sys_org_code}',depart_name,depart_name"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属公司!'},
          ];
     },
  },
  {
    label: '所在部门',
    field: 'affiliatedDept',
    component: 'Input',
  },
  {
    label: '设备图片',
    field: 'imageInfo',
     component: 'JImageUpload',
     componentProps:{
        fileMax: 0
      },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  number: {title: '设备编号',order: 0,view: 'text', type: 'string',},
  epcNumber: {title: '标签EPC',order: 1,view: 'list', type: 'string',dictTable: "base_tag", dictCode: 'epc_number', dictText: 'epc_number',},
  type: {title: '设备类型',order: 2,view: 'list', type: 'string',dictTable: "base_equipment_type", dictCode: 'name', dictText: 'name',},
  affiliatedCompany: {title: '所属公司',order: 3,view: 'list', type: 'string',dictTable: "sys_depart where org_category = 1", dictCode: 'org_code', dictText: 'depart_name',},
  affiliatedDept: {title: '所在部门',order: 4,view: 'text', type: 'string',},
  currentPosition: {title: '当前位置',order: 5,view: 'text', type: 'string',},
  status: {title: '设备状态',order: 6,view: 'text', type: 'string',},
  updateTime: {title: '更新日期',order: 7,view: 'datetime', type: 'string',},
  imageInfo: {title: '设备图片',order: 8,view: 'image', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}