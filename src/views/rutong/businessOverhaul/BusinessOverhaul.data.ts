import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '故障来源',
    align:"center",
    dataIndex: 'source'
   },
   {
    title: '设备编号',
    align:"center",
    dataIndex: 'name_dictText'
   },
   {
    title: 'EPC号',
    align:"center",
    dataIndex: 'epc'
   },
   {
    title: '设备类型',
    align:"center",
    dataIndex: 'type'
   },
   {
    title: '位置',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '故障描述',
    align:"center",
    dataIndex: 'descInfo'
   },
   {
    title: '处理人',
    align:"center",
    dataIndex: 'processor'
   },
   {
    title: '故障状态',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];


//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '故障来源',
    field: 'source',
    component: 'Input',
  },
  {
    label: '设备编号',
    field: 'name',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"base_equipment_management,number,number"
     },
  },
  {
    label: '故障描述',
    field: 'descInfo',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];



//表单数据
export const formSchemaHandle: FormSchema[] = [
  {
    label: '处理类型',
    field: 'processingType',
    component: 'Select',
    componentProps: {
      options: [
        { label: '维修', value: '维修' },
        { label: '更换', value: '更换' },
      ],
      value: '维修',  // 设置默认值
      // disabled: true,  // 禁用选择框
    },
    required: true,    // 设置为必填
  },
  {
    label: '处理备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];




// 高级查询数据
export const superQuerySchema = {
  source: {title: '故障来源',order: 0,view: 'text', type: 'string',},
  name: {title: '设备编号',order: 1,view: 'list', type: 'string',dictTable: "base_equipment_management", dictCode: 'number', dictText: 'number',},
  epc: {title: 'EPC号',order: 2,view: 'text', type: 'string',},
  type: {title: '设备类型',order: 3,view: 'text', type: 'string',},
  address: {title: '位置',order: 4,view: 'text', type: 'string',},
  descInfo: {title: '故障描述',order: 5,view: 'text', type: 'string',},
  processor: {title: '处理人',order: 6,view: 'text', type: 'string',},
  status: {title: '故障状态',order: 7,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}