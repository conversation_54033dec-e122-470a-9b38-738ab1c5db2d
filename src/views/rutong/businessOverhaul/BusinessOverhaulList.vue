<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'rutong:business_overhaul:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button  type="primary" v-auth="'rutong:business_overhaul:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button> -->
        <!-- <j-upload-button type="primary" v-auth="'rutong:business_overhaul:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'rutong:business_overhaul:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <BusinessOverhaulModal @register="registerModal" @success="handleSuccess"></BusinessOverhaulModal>
     <!-- 处理 -->
    <BusinessOverhaulModalHandle @register="registerHandleModal" @success="handleSuccess"></BusinessOverhaulModalHandle>
  </div>
</template>

<script lang="ts" name="rutong-businessOverhaul" setup>
import { ref, reactive, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import BusinessOverhaulModal from './components/BusinessOverhaulModal.vue';
import BusinessOverhaulModalHandle from './components/BusinessOverhaulModalHandle.vue';

import { columns, searchFormSchema, superQuerySchema } from './BusinessOverhaul.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './BusinessOverhaul.api';
import { downloadFile } from '/@/utils/common/renderUtils';
import { useUserStore } from '/@/store/modules/user';
const queryParam = reactive<any>({});
const checkedKeys = ref<Array<string | number>>([]);
const userStore = useUserStore();
//注册model
const [registerModal, { openModal }] = useModal();

const [registerHandleModal, { openModal: openHandleModal }] = useModal();
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '设备检修',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToNumber: [],
      fieldMapToTime: [],
    },
    actionColumn: {
      width: 120,
      fixed: 'right',
    },
    beforeFetch: (params) => {
      return Object.assign(params, queryParam);
    },
  },
  exportConfig: {
    name: '设备检修',
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

// 高级查询配置
const superQueryConfig = reactive(superQuerySchema);

/**
 * 高级查询事件
 */
function handleSuperQuery(params) {
  Object.keys(params).map((k) => {
    queryParam[k] = params[k];
  });
  reload();
}
/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 详情
 */
function handleDetail(record: Recordable) {
  openHandleModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}

/**
 * 处理数据
 */
function handleData(record: Recordable) {
  openHandleModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
}
/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
   if (record.status == '处理中') {
    return [
      {
        label: '处理',
        onClick: handleData.bind(null, record),
      }
    ];
  } else {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }
    ];
  }
  // return [
  //   {
  //     label: '编辑',
  //     onClick: handleEdit.bind(null, record),
  //     auth: 'rutong:business_overhaul:edit',
  //   },
  // ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  console.log(record);
  if (record.status == '处理中') {
    return [
      {
        label: '处理',
        onClick: handleData.bind(null, record),
      }
    ];
  } else {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }
    ];
  }
}
</script>

<style lang="less" scoped>
:deep(.ant-picker),
:deep(.ant-input-number) {
  width: 100%;
}
</style>