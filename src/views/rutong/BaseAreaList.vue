<template>
  <div class="p-2">
    <a-row :gutter="24" style="height: 85vh">
      <!-- 左侧表格区域 -->
      <a-col :span="8">
        <div class="jeecg-basic-table-form-container">
          <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
            <a-row :gutter="24">
              <a-col :lg="18">
                <a-form-item name="name">
                  <template #label><span title="名称">名称</span></template>
                  <a-input v-model:value="queryParam.name" class="query-group-cust" />
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                  <a-col :lg="6">
                    <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                  </a-col>
                </span>
              </a-col>
            </a-row>
          </a-form>
          <BasicTable @register="registerTable">
            <template #tableTitle>
              <a-button type="primary" v-auth="'rutong:base_area:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">新增</a-button>
            </template>
            <template #action="{ record }">
              <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
            </template>
          </BasicTable>
        </div>
      </a-col>

      <!-- 右侧地图区域 -->
      <a-col :span="16">
        <Gaode ref="mapRef2" :width="'100%'" :height="'100%'" :mapOptions="mapOptions" />
      </a-col>
    </a-row>
  </div>

  <BaseAreaModal ref="registerModal" @success="handleSuccess"></BaseAreaModal>
</template>

<script lang="ts" name="rutong-baseArea" setup>
import { ref, reactive, onMounted, defineComponent, nextTick, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, superQuerySchema } from './BaseArea.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './BaseArea.api';
import { downloadFile } from '/@/utils/common/renderUtils';
import BaseAreaModal from './components/BaseAreaModal.vue';

import Gaode from '../demo/charts/map/Gaode.vue';

import { useUserStore } from '/@/store/modules/user';

const formRef = ref();
const queryParam = reactive<any>({});
const toggleSearchStatus = ref<boolean>(false);
const registerModal = ref();
const userStore = useUserStore();

const mapRef2 = ref<any>(null);

const mapOptions = {
  zoom: 14.5,
  center: [121.34113, 31.1959],
  viewMode: '3D',
};

function logMapCenter() {
  const map = mapRef2.value?.map; // 获取子组件暴露的 map 实例
  if (map) {
    console.log('当前地图中心:', map.getCenter());
  }
}

//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '区域管理',
    api: list,
    columns,
    canResize: false,
    useSearchForm: false,

    beforeFetch: async (params) => {
      return Object.assign(params, queryParam);
    },
  },
  exportConfig: {
    name: '区域管理',
    url: getExportUrl,
    params: queryParam,
  },
  importConfig: {
    url: getImportUrl,
    success: handleSuccess,
  },
});
const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
  tableContext;
const labelCol = reactive({
  xs: 24,
  sm: 4,
  xl: 6,
  xxl: 4,
});
const wrapperCol = reactive({
  xs: 24,
  sm: 20,
});

// 高级查询配置
const superQueryConfig = reactive(superQuerySchema);

/**
 * 高级查询事件
 */
function handleSuperQuery(params) {
  Object.keys(params).map((k) => {
    queryParam[k] = params[k];
  });
  searchQuery();
}

/**
 * 新增事件
 */
function handleAdd() {
  registerModal.value.disableSubmit = false;
  registerModal.value.add();
}

/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  registerModal.value.disableSubmit = false;
  registerModal.value.edit(record);
}

/**
 * 详情
 */
function handleDetail(record: Recordable) {
  registerModal.value.disableSubmit = true;
  registerModal.value.edit(record);
}

/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, handleSuccess);
}

/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
}

/**
 * 成功回调
 */
function handleSuccess() {
  (selectedRowKeys.value = []) && reload();
}

/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
      auth: 'rutong:base_area:edit',
    },
  ];
}

/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    {
      label: '详情',
      onClick: handleDetail.bind(null, record),
    },
    {
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
        placement: 'topLeft',
      },
      auth: 'rutong:base_area:delete',
    },
  ];
}

/**
 * 查询
 */
function searchQuery() {
  logMapCenter();
  reload();
}

/**
 * 重置
 */
function searchReset() {
  formRef.value.resetFields();
  selectedRowKeys.value = [];
  //刷新数据
  reload();
}

async function initializeTableData() {
  await reload(); // 等待数据加载完成
  const tableData = getDataSource(); // 获取数据

  const map2 = mapRef2.value?.map; // 获取子组件暴露的 map 实例
  console.log(tableData);

  const AMap = (window as any).AMap;
  if (!AMap) {
    console.error('未能找到 AMap 对象，请检查高德地图脚本是否正确加载');
    return;
  }

  tableData.forEach((item, index) => {
    console.log(`第 ${index + 1} 条数据:`, item);
    // 你可以根据需要在这里对 item 做更多的处理
    // 示例：添加多边形
    // 解析字符串坐标
    const path1 = [
      item.coordinate.split(',').map(Number),
      item.coordinate2.split(',').map(Number),
      item.coordinate3.split(',').map(Number),
      item.coordinate4.split(',').map(Number),
    ];
    const polygon1 = new AMap.Polygon({
      path: path1,
      strokeColor: item.color,
      strokeWeight: 6,
      strokeOpacity: 0.2,
      fillOpacity: 0.4,
      fillColor: item.color,
      zIndex: 50,
      bubble: true,
    });
    map2.add([polygon1]);

  // 添加文字标注
    const center = polygon1.getBounds().getCenter(); // 获取多边形中心点
    const text = new AMap.Text({
      text: item.name, // 标注内容
      anchor: 'center', // 设置文本居中
      draggable: false, // 禁止拖拽
      style: {
        color: item.color, // 文字颜色
        fontSize: '14px', // 文字大小
        fontWeight: 'bold', // 文字加粗
        // backgroundColor: 'transparent', // 背景透明
        border: 'none', // 无边框
      },
      position: center, // 设置文字位置
    });

    map2.add(text); // 将文字添加到地图


  });

  console.log('初始化表格数据:', tableData);
}

onMounted(() => {
  initializeTableData();
});
</script>

<style lang="less" scoped>
.jeecg-basic-table-form-container {
  padding: 0;
  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
  .query-group-cust {
    min-width: 100px !important;
  }
  .query-group-split-cust {
    width: 30px;
    display: inline-block;
    text-align: center;
  }
  .ant-form-item:not(.ant-form-item-with-help) {
    margin-bottom: 16px;
    height: 32px;
  }
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
}
</style>
