import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/track',
    name: 'Track', // 主页面
    component: () => import('/@/views/rutong/realTimeMonitoring/components/TrackPage.vue'),
    meta: {
      title: '设备管理',
    },
  },
  {
    path: '/track/details', // 详情页面
    name: 'TrackDetails',
    component: () => import('/@/views/rutong/realTimeMonitoring/components/TrackPage.vue'),
    meta: {
      title: '轨迹详情',
    },
  },
];

export default routes;